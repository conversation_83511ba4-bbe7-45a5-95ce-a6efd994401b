/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable no-case-declarations */
import styles from './theme-preview.module.scss'
import classNamesBind from 'classnames/bind'
import { useEffect, useState } from 'react'
import { OutputData } from '@editorjs/editorjs'
import { ManageQuestionsFormState } from '@/features/manage-questions/views/manage-questions-form'
import { TStep } from '@/entities/themeCourse/model/types'
import i18n from 'i18next'
import { useAppDispatch, useAppSelector } from '@/store'
import { resetThemePageState } from '@/store/slices/theme-page-slice'
import { ThemePreviewProps } from './theme-preview.d'
import { Step, StepsToolbarPreview, TMinifiedStepType } from './components/steps-toolbar-preview'
import { ThemeContentPreview } from './components/theme-content-preview'
import {
  checkCurrentStep,
  selectActiveStep,
  selectData,
  selectDisableFetch,
  selectDisableNext,
  selectSectionId,
  selectTheme,
  selectTheme<PERSON>rogress,
  setActiveStepHandler,
  setData,
  setDisableNext,
  setUserCourseActiveStep,
} from '@/store/slices/user-course-slice'
import { testApi } from '@/entities/courses/model/api/endpoints'
import { useParams } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { Button } from '@/shared/ui'
import DownloadIcon from '@/shared/ui/Icon/icons/components/DownloadIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { handleActiveData } from './helpers'
import { useAnalytics } from '@/shared/hooks/use-analytics'
import { GOALS } from '@/shared/constants'

const cx = classNamesBind.bind(styles)

const TRANSLATION_FILE = 'pages__learning__user-course'

export type DataType = OutputData | ManageQuestionsFormState | { path: string }

const Placeholder = () => {
  const { t } = useTranslation(TRANSLATION_FILE)
  const reload = () => window.location.reload()

  return (
    <div className={cx('placeholder')}>
      <div className={cx('text')}>{t('placeholder')}</div>
      <Button size='big' color='green' onClick={reload}>
        {t('reload')}
      </Button>
    </div>
  )
}

export const ThemePreview: React.FC<ThemePreviewProps.Props> = ({
  themeData,
  isUser,
  showPresButton,
  refetchProgress,
  contentWrapperClassName,
}) => {
  const dispatch = useAppDispatch()
  const themeProgress = useAppSelector(selectThemeProgress)
  const currentSection = useAppSelector(selectSectionId)
  const currentTheme = useAppSelector(selectTheme)
  const disableFetch = useAppSelector(selectDisableFetch)
  const disableNext = useAppSelector(selectDisableNext)
  const { course_id = '' } = useParams()
  const analytics = useAnalytics()

  const { t } = useTranslation('pages__create-theme')

  useEffect(() => {
    i18n.loadNamespaces([
      'components__custom-image',
      'components__custom-audio',
      'components__custom-video',
    ])

    return () => {
      dispatch(resetThemePageState())
    }
  }, [])

  const data = useAppSelector(selectData)
  const [minifiedSteps, setMinifiedSteps] = useState<Step[]>([])
  const activeStep = useAppSelector(selectActiveStep)
  const handleActive = async (step: Step | undefined) => {
    await dispatch(setUserCourseActiveStep(step))
  }

  const [startStep] = testApi.useInitStepsMutation()

  useEffect(() => {
    const handleStart = async () => {
      if (disableNext) return
      if (themeProgress && themeProgress.started && activeStep && isUser && refetchProgress) {
        const isStarted = themeProgress.steps?.find(s => s?.step_id === activeStep.id)?.started

        if (isStarted === false && activeStep.type !== 'quiz' && currentSection && currentTheme) {
          await dispatch(setDisableNext(true))
          await startStep({
            body: {
              section_id: currentSection,
              theme_id: currentTheme?.theme_id,
              step_id: activeStep?.id,
            },
            assigned_course_id: course_id,
          }).unwrap()

          await refetchProgress().unwrap()
          dispatch(setDisableNext(false))
        }
      }
    }
    handleStart()
  }, [activeStep, themeProgress])

  useEffect(() => {
    if (activeStep && isUser) {
      dispatch(checkCurrentStep())
    }
    if (
      activeStep &&
      refetchProgress &&
      !disableFetch &&
      isUser &&
      themeProgress?.theme_id === themeData?.id
    ) {
      //TODO: temporary. Later think about how minimize amount of progress requests
      refetchProgress().unwrap()
    }
  }, [activeStep])

  const handleThemeStepsChange = (step?: TStep, changeActive: boolean = false) => {
    if (changeActive && step) {
      handleActive({
        id: step?.id,
        type: step.type as TMinifiedStepType,
      })
    }
    const minified = themeData?.steps.map((s: TStep) => ({
      id: s?.id,
      type: s?.type,
      isNew: false,
    }))
    setMinifiedSteps(minified as Step[])
  }

  useEffect(() => {
    const handle = async () => {
      await dispatch(setData(undefined))
      await handleActiveData({ dispatch, activeStep, themeData, stepData: themeData.steps[0] })
      await handleThemeStepsChange(themeData.steps[0], true)
    }

    if (!!themeData && !!themeData.steps?.length) {
      handle()
    }
    if (!themeData?.steps?.length) {
      dispatch(setData(undefined))
      handleActive(undefined)
      setMinifiedSteps([])
    }
  }, [themeData?.steps])

  const handleActiveStep = async (item: Step) => {
    dispatch(setData(undefined))
    handleActiveData({ dispatch, activeStep: item, themeData })
    //TODO: remove timeout
    setTimeout(() => handleActive(item), 0)
  }

  useEffect(() => {
    dispatch(setActiveStepHandler(handleActiveStep))
  }, [handleActiveStep])

  if (isUser && currentSection && currentTheme && !themeData) {
    return <Placeholder />
  }

  const handleDownloadClick = () => {
    analytics.event(GOALS['course-download-pptx'].name, { id: course_id, title: themeData.title })
  }

  return (
    <div className={cx('wrapper')}>
      {showPresButton ? (
        <div className={cx('topWrapper')}>
          <StepsToolbarPreview
            steps={minifiedSteps}
            activeStep={activeStep}
            isUser={isUser}
            setActive={handleActiveStep}
          />
          {themeData?.pptx_path && (
            <a className={cx('download')} onClick={handleDownloadClick} href={themeData.pptx_path}>
              {t('download_presentation')}
              <IconWrapper color='primary'>
                <DownloadIcon />
              </IconWrapper>
            </a>
          )}
        </div>
      ) : (
        <StepsToolbarPreview
          steps={minifiedSteps}
          activeStep={activeStep}
          isUser={isUser}
          setActive={handleActiveStep}
        />
      )}
      <ThemeContentPreview
        wrapperClassName={contentWrapperClassName}
        data={data}
        setData={d => dispatch(setData(d))}
        type={activeStep?.type}
        stepId={activeStep?.id}
        themeData={themeData}
        isUser={isUser}
      />
    </div>
  )
}
