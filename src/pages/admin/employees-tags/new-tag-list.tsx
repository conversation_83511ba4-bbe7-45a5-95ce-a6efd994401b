import { useCallback, useEffect, useState } from 'react'
import { v4 as uuid } from 'uuid'
import styles from './tags-list.module.scss'
import classNamesBind from 'classnames/bind'
import { Tag } from '@/shared/components'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  createTag,
  deleteTagById,
  invalidateAllTags,
  selectActiveTag,
  selectAllTags,
  selectCustomTags,
  selectOtherTags,
  selectPage,
  selectRiskGroupTags,
  setActiveTag,
  setPage,
} from '@/store/slices/tags'
import { Button, Loader } from '@/shared/ui'

import {
  tagsEmployeesApi,
  useGetOrganizationTagsQuery,
} from '@/store/services/tags-employees-service'
import DeleteCustomTagModal from '@/shared/modals/delete-custom-tag-modal/delete-custom-tag-modal'
import { useTranslation } from 'react-i18next'
import { useUserOrganizationId } from '@/entities/employee'
import { ETagType } from '@/shared/types/enums'
import InfiniteScroll from 'react-infinite-scroll-component'
import { useNotification } from '@/shared/contexts/notifications'

const cx = classNamesBind.bind(styles)

export const NewTagsList = () => {
  const dispatch = useAppDispatch()
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false)
  const [deleteId, setDeleteId] = useState<UUID | null>(null)
  const { t } = useTranslation('pages__employees')
  const page = useAppSelector(selectPage)
  const { add } = useNotification()

  useEffect(() => {
    return () => {
      dispatch(invalidateAllTags())
    }
  }, [dispatch])

  useGetOrganizationTagsQuery({
    limit: 20,
    offset: (page - 1) * 20,
  })

  const activeTag = useAppSelector(selectActiveTag)
  const riskGroupTags = useAppSelector(selectRiskGroupTags)
  const otherTags = useAppSelector(selectOtherTags)
  const customTags = useAppSelector(selectCustomTags)
  const organizationTags = useAppSelector(selectAllTags)

  const userOrganizationId = useUserOrganizationId()

  const handleAddTagClick = useCallback(async () => {
    const tagData = {
      id: uuid(),
      type: ETagType.arbitrary,
      title: t('new_tag'),
      color: '#B5B8B1',
      settings: {
        max_risk_level: null,
        min_risk_level: null,
        life_days: null,
        mail_invite_interval: null,
        mail_invite_time: null,
        mail_last_invite_date: null,
      },
      is_active: true,
      organization_id: userOrganizationId || '',
    }

    await dispatch(createTag(tagData)).unwrap()

    add({
      id: crypto.randomUUID(),
      message: t('add_tag_success'),
      status: 'success',
    })

    // Сохраняем значение до всех запросов
    const countBefore = organizationTags.total_count

    // Через 10 секунд — первый запрос
    setTimeout(() => {
      ;(async () => {
        const { total_count: countAfter } = await dispatch(
          tagsEmployeesApi.endpoints.getOrganizationTags.initiate({
            limit: 20,
            offset: (page - 1) * 20,
          }),
        ).unwrap()

        // Если ничего не изменилось — через 5 секунд делаем повторный запрос
        if (countAfter === countBefore) {
          setTimeout(() => {
            ;(async () => {
              await dispatch(
                tagsEmployeesApi.endpoints.getOrganizationTags.initiate({
                  limit: 20,
                  offset: (page - 1) * 20,
                }),
              ).unwrap()
            })()
          }, 5000)
        }
      })()
    }, 10000)
  }, [t, userOrganizationId, dispatch, add, organizationTags.total_count, page])

  const handleDeleteCustomTagClick = useCallback((tagId: UUID) => {
    setDeleteId(tagId)
    setIsDeleteModalOpen(true)
  }, [])

  const handleConfirmDeleteClick = useCallback(async () => {
    if (!deleteId) return
    await dispatch(deleteTagById(deleteId)).unwrap()
    setDeleteId(null)
    setIsDeleteModalOpen(false)
    add({
      id: crypto.randomUUID(),
      message: t('delete_tag_success'),
      status: 'success',
    })
  }, [add, deleteId, dispatch, t])

  const handleCancelDeleteClick = useCallback(() => {
    setDeleteId(null)
    setIsDeleteModalOpen(false)
  }, [])

  const totalCount = customTags?.total_count ?? 0
  const hasMore = page * 20 < totalCount

  return (
    <div className={cx('wrapper')}>
      {!!otherTags?.length && (
        <>
          <div className={cx('title')}>{t('on_board')}</div>
          <div className={cx('tags')}>
            {otherTags.map(tag => {
              const isActive = activeTag?.id === tag.id

              return (
                <div
                  key={`tag-${tag.id}`}
                  className={cx('tagWrapper')}
                  onClick={() => dispatch(setActiveTag(tag))}
                >
                  <Tag info={tag} isActive={isActive} />
                  {isActive ? <span>{t('commons:selected')}</span> : null}
                </div>
              )
            })}
          </div>
        </>
      )}
      {!!riskGroupTags?.length && (
        <>
          <div className={cx('title')}>{t('risk_group')}</div>
          <div className={cx('tags')}>
            {riskGroupTags.map(tag => {
              const isActive = activeTag?.id === tag.id

              return (
                <div
                  key={`tag-${tag.id}`}
                  className={cx('tagWrapper')}
                  onClick={() => dispatch(setActiveTag(tag))}
                >
                  <Tag info={tag} isActive={isActive} />
                  {isActive ? <span>{t('commons:selected')}</span> : null}
                </div>
              )
            })}
          </div>
        </>
      )}

      <div className={cx('title')}>{t('custom')}</div>
      {customTags?.data?.length > 0 && (
        <div id='tags-list' className={cx('tags', 'scrollbar')}>
          <InfiniteScroll
            dataLength={totalCount}
            next={() => dispatch(setPage(page + 1))}
            hasMore={hasMore}
            loader={<Loader size='56' loading />}
            scrollableTarget='tags-list'
          >
            {!!customTags?.data.length &&
              customTags?.data.map(tag => {
                const isActive = activeTag?.id === tag.id

                return (
                  <div
                    key={`tag-${tag.id}`}
                    className={cx('tagWrapper')}
                    onClick={() => dispatch(setActiveTag(tag))}
                  >
                    <Tag
                      info={tag}
                      isActive={isActive}
                      onDelete={() => handleDeleteCustomTagClick(tag.id)}
                    />
                    {isActive ? <span>{t('commons:selected')}</span> : null}
                  </div>
                )
              })}
          </InfiniteScroll>
        </div>
      )}
      <DeleteCustomTagModal
        active={isDeleteModalOpen}
        onClose={handleCancelDeleteClick}
        onConfirm={handleConfirmDeleteClick as () => Promise<void>}
      />
      <div className={cx('tagWrapper')}>
        <Button leftIcon='plus' size='small' color='darkGray' onClick={handleAddTagClick}>
          {t('add_tag')}
        </Button>
      </div>
    </div>
  )
}
